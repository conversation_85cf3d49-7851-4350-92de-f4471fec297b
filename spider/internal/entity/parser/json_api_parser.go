package parser

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	dapr "github.com/dapr/go-sdk/client"
	_ "github.com/go-sql-driver/mysql"
	"github.com/oliveagle/jsonpath"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"shovel/spider/internal/models"
)

// ParseRule 定义解析规则结构体
type ParseRule struct {
	ParseMode   string `json:"parse_mode"`   // raw 或 jsonpath
	StoreEngine string `json:"store_engine"` // mongodb 或 mysql
	Collection  string `json:"collection"`   // MongoDB collection名称
	TableName   string `json:"tablename"`    // MySQL表名
	JSONPath    string `json:"jsonpath"`     // JSONPath表达式
}

// ParseConfig 定义解析配置结构体
type ParseConfig map[string][]ParseRule

// JSONAPIParser 实现用于解析JSON API的解析器
type JSONAPIParser struct {
	mongoClient     *mongo.Client
	mongoDatabase   string // MongoDB数据库名称
	mongoCollection string // 默认MongoDB collection
	mysqlClient     *sql.DB
	mysqlTablename  string // 默认MySQL表名
}

// NewJSONAPIParser 创建新的JSON API解析器实例
func NewJSONAPIParser() (*JSONAPIParser, error) {
	// 创建Dapr客户端
	daprClient, err := dapr.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}
	defer daprClient.Close()

	// 从环境变量获取配置，如果环境变量中没有，则从配置存储获取
	mongoURI := os.Getenv("MONGO_URI")
	if mongoURI == "" {
		mongoURIConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mongo_uri")
		if err != nil {
			return nil, fmt.Errorf("获取MongoDB URI配置失败: %w", err)
		}
		if mongoURIConfig == nil {
			return nil, fmt.Errorf("MongoDB URI配置为空")
		}
		mongoURI = mongoURIConfig.Value
	}

	databaseName := os.Getenv("MONGO_DATABASE")
	if databaseName == "" {
		databaseConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mongo_database")
		if err != nil {
			return nil, fmt.Errorf("获取MongoDB数据库名称配置失败: %w", err)
		}
		if databaseConfig == nil {
			return nil, fmt.Errorf("MongoDB数据库名称配置为空")
		}
		databaseName = databaseConfig.Value
	}

	collectionName := os.Getenv("MONGO_COLLECTION")
	if collectionName == "" {
		collectionConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mongo_collection")
		if err != nil {
			return nil, fmt.Errorf("获取MongoDB集合名称配置失败: %w", err)
		}
		if collectionConfig == nil {
			return nil, fmt.Errorf("MongoDB集合名称配置为空")
		}
		collectionName = collectionConfig.Value
	}

	// 创建MongoDB客户端
	clientOptions := options.Client().ApplyURI(mongoURI)
	client, err := mongo.Connect(context.Background(), clientOptions)
	if err != nil {
		return nil, fmt.Errorf("连接MongoDB失败: %w", err)
	}

	// 检查连接
	err = client.Ping(context.Background(), nil)
	if err != nil {
		return nil, fmt.Errorf("MongoDB连接测试失败: %w", err)
	}

	// 创建MySQL连接
	mysqlDSN := os.Getenv("MYSQL_DSN")
	if mysqlDSN == "" {
		mysqlDSNConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mysql_dsn")
		if err != nil {
			return nil, fmt.Errorf("获取MySQL DSN配置失败: %w", err)
		}
		if mysqlDSNConfig == nil {
			return nil, fmt.Errorf("MySQL DSN配置为空")
		}
		mysqlDSN = mysqlDSNConfig.Value
	}

	mysqlTablename := os.Getenv("MYSQL_TABLE")
	if mysqlTablename == "" {
		mysqlTableConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mysql_table")
		if err != nil {
			return nil, fmt.Errorf("获取MySQL表名配置失败: %w", err)
		}
		if mysqlTableConfig == nil {
			return nil, fmt.Errorf("MySQL表名配置为空")
		}
		mysqlTablename = mysqlTableConfig.Value
	}

	var mysqlClient *sql.DB
	mysqlClient, err = sql.Open("mysql", mysqlDSN)
	if err != nil {
		return nil, fmt.Errorf("连接MySQL失败: %w", err)
	}

	// 测试连接
	if err := mysqlClient.Ping(); err != nil {
		return nil, fmt.Errorf("MySQL连接测试失败: %w", err)
	}

	parser := &JSONAPIParser{
		mongoClient:     client,
		mongoDatabase:   databaseName,
		mongoCollection: collectionName,
		mysqlClient:     mysqlClient,
		mysqlTablename:  mysqlTablename,
	}

	return parser, nil
}

// Parse 实现Parser接口的Parse方法
func (p *JSONAPIParser) Parse(ctx context.Context, content []byte, urlItem *models.URLQueueItem, parseConfigStr string) (*ParseResult, error) {
	// 解析配置
	var config ParseConfig
	if err := json.Unmarshal([]byte(parseConfigStr), &config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	// 获取页面类型对应的解析规则
	rules, exists := config[urlItem.PageType]
	if !exists {
		return nil, fmt.Errorf("未找到页面类型 %s 的解析规则", urlItem.PageType)
	}

	// 检查JSON是否合法
	var jsonData interface{}
	if err := json.Unmarshal(content, &jsonData); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %w", err)
	}

	// 处理每个解析规则
	for _, rule := range rules {
		if err := p.processRule(ctx, content, jsonData, urlItem, rule); err != nil {
			return nil, fmt.Errorf("处理解析规则失败: %w", err)
		}
	}

	// 这个解析器不会生成新的URL队列项
	return &ParseResult{
		Items: []*models.URLQueueItem{},
		Data:  jsonData,
	}, nil
}

// processRule 处理单个解析规则
func (p *JSONAPIParser) processRule(ctx context.Context, content []byte, jsonData interface{}, urlItem *models.URLQueueItem, rule ParseRule) error {
	switch rule.ParseMode {
	case "raw":
		return p.processRawMode(ctx, content, urlItem, rule)
	case "jsonpath":
		return p.processJSONPathMode(ctx, jsonData, urlItem, rule)
	default:
		return fmt.Errorf("不支持的解析模式: %s", rule.ParseMode)
	}
}

// processRawMode 处理raw解析模式
func (p *JSONAPIParser) processRawMode(ctx context.Context, content []byte, urlItem *models.URLQueueItem, rule ParseRule) error {
	switch rule.StoreEngine {
	case "mongodb":
		return p.saveRawToMongoDB(ctx, content, urlItem, rule)
	case "mysql":
		return p.saveRawToMySQL(ctx, content, urlItem, rule)
	default:
		return fmt.Errorf("不支持的存储引擎: %s", rule.StoreEngine)
	}
}

// processJSONPathMode 处理jsonpath解析模式
func (p *JSONAPIParser) processJSONPathMode(ctx context.Context, jsonData interface{}, urlItem *models.URLQueueItem, rule ParseRule) error {
	if rule.JSONPath == "" {
		return fmt.Errorf("jsonpath模式下必须配置jsonpath字段")
	}

	switch rule.StoreEngine {
	case "mongodb":
		if rule.Collection == "" {
			return fmt.Errorf("jsonpath模式下使用mongodb存储必须配置collection字段")
		}
		return p.saveJSONPathToMongoDB(ctx, jsonData, urlItem, rule)
	case "mysql":
		if rule.TableName == "" {
			return fmt.Errorf("jsonpath模式下使用mysql存储必须配置tablename字段")
		}
		return p.saveJSONPathToMySQL(ctx, jsonData, urlItem, rule)
	default:
		return fmt.Errorf("不支持的存储引擎: %s", rule.StoreEngine)
	}
}

// saveRawToMongoDB 保存raw数据到MongoDB
func (p *JSONAPIParser) saveRawToMongoDB(ctx context.Context, content []byte, urlItem *models.URLQueueItem, rule ParseRule) error {
	// 确定collection名称
	collectionName := rule.Collection
	if collectionName == "" {
		collectionName = p.mongoCollection
	}

	// 获取collection
	collection := p.mongoClient.Database(p.mongoDatabase).Collection(collectionName)

	// 保存数据
	_, err := collection.InsertOne(ctx, map[string]interface{}{
		"data":        string(content),
		"task_id":     urlItem.TaskID,
		"create_time": time.Now(),
	})

	if err != nil {
		return fmt.Errorf("保存raw数据到MongoDB失败: %w", err)
	}

	return nil
}

// saveRawToMySQL 保存raw数据到MySQL
func (p *JSONAPIParser) saveRawToMySQL(ctx context.Context, content []byte, urlItem *models.URLQueueItem, rule ParseRule) error {
	if p.mysqlClient == nil {
		return fmt.Errorf("MySQL连接未初始化")
	}

	// 确定表名
	tableName := rule.TableName
	if tableName == "" {
		tableName = p.mysqlTablename
	}

	// 插入数据
	query := fmt.Sprintf("INSERT INTO %s (data, task_id, create_time) VALUES (?, ?, ?)", tableName)
	_, err := p.mysqlClient.ExecContext(ctx, query, string(content), urlItem.TaskID, time.Now())

	if err != nil {
		return fmt.Errorf("保存raw数据到MySQL失败: %w", err)
	}

	return nil
}

// saveJSONPathToMongoDB 保存jsonpath解析结果到MongoDB
func (p *JSONAPIParser) saveJSONPathToMongoDB(ctx context.Context, jsonData interface{}, urlItem *models.URLQueueItem, rule ParseRule) error {
	// 使用oliveagle/jsonpath提取数据
	result, err := jsonpath.JsonPathLookup(jsonData, rule.JSONPath)
	if err != nil {
		return fmt.Errorf("jsonpath解析失败: %w", err)
	}

	// 获取collection
	collection := p.mongoClient.Database(p.mongoDatabase).Collection(rule.Collection)

	// 处理提取结果
	switch v := result.(type) {
	case []interface{}:
		// 如果是数组，批量插入
		var documents []interface{}
		for _, item := range v {
			var doc map[string]interface{}
			if itemMap, ok := item.(map[string]interface{}); ok {
				// 如果是对象，复制所有字段
				doc = make(map[string]interface{})
				for key, value := range itemMap {
					doc[key] = value
				}
			} else {
				// 如果不是对象，作为data字段
				doc = map[string]interface{}{"data": item}
			}
			documents = append(documents, doc)
		}
		if len(documents) > 0 {
			_, err = collection.InsertMany(ctx, documents)
		}
	default:
		// 如果是单个对象
		var doc map[string]interface{}
		if itemMap, ok := result.(map[string]interface{}); ok {
			// 如果是对象，复制所有字段
			doc = make(map[string]interface{})
			for key, value := range itemMap {
				doc[key] = value
			}
		} else {
			// 如果不是对象，作为data字段
			doc = map[string]interface{}{"data": result}
		}
		_, err = collection.InsertOne(ctx, doc)
	}

	if err != nil {
		return fmt.Errorf("保存jsonpath结果到MongoDB失败: %w", err)
	}

	return nil
}

// saveJSONPathToMySQL 保存jsonpath解析结果到MySQL
func (p *JSONAPIParser) saveJSONPathToMySQL(ctx context.Context, jsonData interface{}, urlItem *models.URLQueueItem, rule ParseRule) error {
	if p.mysqlClient == nil {
		return fmt.Errorf("MySQL连接未初始化")
	}

	// 使用oliveagle/jsonpath提取数据
	result, err := jsonpath.JsonPathLookup(jsonData, rule.JSONPath)
	if err != nil {
		return fmt.Errorf("jsonpath解析失败: %w", err)
	}

	// 处理提取结果
	switch v := result.(type) {
	case []interface{}:
		// 如果是数组，批量插入
		for _, item := range v {
			if err := p.insertJSONObjectToMySQL(ctx, item, rule.TableName); err != nil {
				return err
			}
		}
	default:
		// 如果是单个对象
		if err := p.insertJSONObjectToMySQL(ctx, result, rule.TableName); err != nil {
			return err
		}
	}

	return nil
}

// insertJSONObjectToMySQL 将JSON对象插入到MySQL表中
// 根据JSON对象的字段名作为列名，字段值作为列值插入
// 支持一层子对象扩展，将子对象的字段以 "父字段名_子字段名" 的形式作为列名
func (p *JSONAPIParser) insertJSONObjectToMySQL(ctx context.Context, data interface{}, tableName string) error {
	// 将数据转换为map[string]interface{}
	var dataMap map[string]interface{}

	switch v := data.(type) {
	case map[string]interface{}:
		dataMap = v
	case string:
		// 如果是字符串，尝试解析为JSON
		if err := json.Unmarshal([]byte(v), &dataMap); err != nil {
			// 如果解析失败，作为单个值处理
			dataMap = map[string]interface{}{"value": v}
		}
	default:
		// 其他类型，尝试序列化后再解析
		jsonBytes, err := json.Marshal(data)
		if err != nil {
			return fmt.Errorf("序列化数据失败: %w", err)
		}

		if err := json.Unmarshal(jsonBytes, &dataMap); err != nil {
			// 如果解析失败，作为单个值处理
			dataMap = map[string]interface{}{"value": data}
		}
	}

	if len(dataMap) == 0 {
		return fmt.Errorf("没有可插入的数据")
	}

	// 扁平化数据，处理嵌套对象
	flattenedData := p.flattenJSONData(dataMap)

	// 构建INSERT语句
	var columns []string
	var placeholders []string
	var values []interface{}

	for column, value := range flattenedData {
		columns = append(columns, column)
		placeholders = append(placeholders, "?")
		values = append(values, value)
	}

	query := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
		tableName,
		strings.Join(columns, ", "),
		strings.Join(placeholders, ", "))

	_, err := p.mysqlClient.ExecContext(ctx, query, values...)
	if err != nil {
		return fmt.Errorf("插入数据到MySQL表 %s 失败: %w", tableName, err)
	}

	return nil
}

// flattenJSONData 扁平化JSON数据，将一层嵌套对象展开为带前缀的字段
// 只处理一层嵌套，更深层的嵌套将作为JSON字符串保存
func (p *JSONAPIParser) flattenJSONData(data map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	for key, value := range data {
		switch v := value.(type) {
		case map[string]interface{}:
			// 处理嵌套对象，只展开一层
			for subKey, subValue := range v {
				flattenedKey := fmt.Sprintf("%s_%s", key, subKey)

				// 检查子值是否还是对象，如果是则转为JSON字符串
				if subMap, isMap := subValue.(map[string]interface{}); isMap {
					jsonBytes, err := json.Marshal(subMap)
					if err != nil {
						// 如果序列化失败，使用原始值
						result[flattenedKey] = subValue
					} else {
						result[flattenedKey] = string(jsonBytes)
					}
				} else if subArray, isArray := subValue.([]interface{}); isArray {
					// 如果是数组，也转为JSON字符串
					jsonBytes, err := json.Marshal(subArray)
					if err != nil {
						// 如果序列化失败，使用原始值
						result[flattenedKey] = subValue
					} else {
						result[flattenedKey] = string(jsonBytes)
					}
				} else {
					// 基本类型直接使用
					result[flattenedKey] = subValue
				}
			}
		case []interface{}:
			// 数组类型转为JSON字符串
			jsonBytes, err := json.Marshal(v)
			if err != nil {
				// 如果序列化失败，使用原始值
				result[key] = value
			} else {
				result[key] = string(jsonBytes)
			}
		case nil:
			// null值保持为nil
			result[key] = nil
		default:
			// 基本类型（string, number, boolean）直接使用
			result[key] = value
		}
	}

	return result
}

// Close 关闭连接
func (p *JSONAPIParser) Close(ctx context.Context) error {
	var err error
	if p.mongoClient != nil {
		if mongoErr := p.mongoClient.Disconnect(ctx); mongoErr != nil {
			err = mongoErr
		}
	}
	if p.mysqlClient != nil {
		if mysqlErr := p.mysqlClient.Close(); mysqlErr != nil {
			if err != nil {
				err = fmt.Errorf("MongoDB关闭错误: %v, MySQL关闭错误: %v", err, mysqlErr)
			} else {
				err = mysqlErr
			}
		}
	}
	return err
}
